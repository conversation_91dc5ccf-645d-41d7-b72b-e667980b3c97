<execution>
  <constraint>
    ## ComfyUI系统技术约束
    - **节点兼容性**：确保使用的节点在目标ComfyUI版本中可用
    - **内存管理**：严格控制显存和内存使用，避免系统崩溃
    - **文件路径规范**：模型文件路径必须符合ComfyUI目录结构
    - **数据类型匹配**：节点间数据传递类型必须完全匹配
  </constraint>

  <rule>
    ## 技术实现强制规则
    - **节点命名规范**：使用清晰的节点标题，便于工作流维护
    - **参数验证机制**：关键参数必须在有效范围内设置
    - **错误处理标准**：提供详细的错误诊断和解决方案
    - **版本控制要求**：明确工作流适用的ComfyUI和插件版本
    - **性能基准线**：确保工作流在标准硬件上可正常运行
  </rule>

  <guideline>
    ## 技术实现指导原则
    - **最佳实践优先**：采用经过验证的技术方案
    - **可维护性设计**：工作流结构清晰，易于调试修改
    - **扩展性考虑**：为未来功能扩展预留接口
    - **用户友好性**：降低技术门槛，提供详细说明
    - **社区兼容性**：与ComfyUI社区标准保持一致
  </guideline>

  <process>
    ## ComfyUI技术标准实施流程
    
    ### Step 1: 环境验证与准备
    
    ```mermaid
    flowchart TD
        A[ComfyUI版本检查] --> B{版本兼容性}
        B -->|兼容| C[插件依赖检查]
        B -->|不兼容| D[版本升级建议]
        C --> E[模型文件验证]
        E --> F[硬件资源评估]
        F --> G[环境准备完成]
        D --> H[提供升级指南]
    ```
    
    **环境检查清单**：
    - ComfyUI版本 ≥ 0.1.0
    - Python版本 ≥ 3.8
    - PyTorch版本兼容性
    - CUDA/ROCm驱动状态
    - 可用显存 ≥ 6GB
    
    ### Step 2: 核心节点标准化配置
    
    ```mermaid
    graph LR
        A[基础节点] --> B[CheckpointLoaderSimple]
        A --> C[VAELoader]
        A --> D[CLIPTextEncode]
        
        E[控制节点] --> F[ControlNetLoader]
        E --> G[IPAdapterModelLoader]
        E --> H[AnimateDiffLoader]
        
        I[生成节点] --> J[KSampler]
        I --> K[VAEDecode]
        I --> L[SaveImage]
    ```
    
    **标准节点配置模板**：
    
    #### CheckpointLoaderSimple
    ```
    必需设置：
    - ckpt_name: 模型文件名(含扩展名)
    输出类型：
    - MODEL, CLIP, VAE
    ```
    
    #### KSampler核心参数
    ```
    标准配置：
    - seed: 随机种子(建议固定用于调试)
    - steps: 20-30 (质量与速度平衡)
    - cfg: 6-12 (提示词遵循度)
    - sampler_name: "dpmpp_2m_karras"
    - scheduler: "karras"
    - denoise: 0.1-1.0 (噪声强度)
    ```
    
    ### Step 3: 高级功能集成标准
    
    ```mermaid
    flowchart TD
        A[ControlNet集成] --> B{控制类型选择}
        B -->|姿态| C[OpenPose预处理器]
        B -->|深度| D[MiDaS深度估计]
        B -->|边缘| E[Canny边缘检测]
        
        C --> F[ControlNet应用]
        D --> F
        E --> F
        F --> G[权重调节优化]
    ```
    
    **ControlNet标准配置**：
    ```
    Apply ControlNet节点：
    - strength: 0.6-1.0 (控制强度)
    - start_percent: 0.0 (开始时机)
    - end_percent: 0.8-1.0 (结束时机)
    ```
    
    **IPAdapter标准配置**：
    ```
    IPAdapter节点：
    - weight: 0.6-0.8 (特征权重)
    - weight_type: "linear"/"ease in-out"
    - combine_embeds: "concat"/"add"/"subtract"
    ```
    
    ### Step 4: 性能优化标准实施
    
    ```mermaid
    graph TD
        A[性能监控] --> B{资源使用率}
        B -->|显存>90%| C[降低分辨率]
        B -->|显存<50%| D[提升质量设置]
        B -->|正常范围| E[维持当前配置]
        
        C --> F[重新评估]
        D --> F
        E --> G[性能基准记录]
    ```
    
    **显存优化策略**：
    - 使用VAE fp16精度：`--fp16-vae`
    - 启用模型卸载：`--lowvram`
    - 批次大小控制：根据显存动态调整
    - 分辨率阶梯：512→768→1024逐步提升
    
    ### Step 5: 质量保证与测试
    
    ```mermaid
    flowchart LR
        A[工作流测试] --> B[功能验证]
        B --> C[性能测试]
        C --> D[兼容性检查]
        D --> E[文档完善]
        E --> F[交付准备]
    ```
    
    **测试检查项**：
    - [ ] 所有节点正常连接
    - [ ] 参数设置在有效范围
    - [ ] 生成结果符合预期
    - [ ] 错误处理机制有效
    - [ ] 性能指标达标
    - [ ] 文档说明完整
  </process>

  <criteria>
    ## 技术标准评价指标
    
    ### 功能完整性
    - ✅ 所有必需节点正确配置
    - ✅ 数据流向清晰无误
    - ✅ 参数设置合理有效
    - ✅ 输出结果稳定可靠
    
    ### 性能表现
    - ✅ 显存使用效率 ≥ 80%
    - ✅ 生成速度满足实用需求
    - ✅ 系统稳定性良好
    - ✅ 错误率 ≤ 5%
    
    ### 兼容性标准
    - ✅ 主流ComfyUI版本兼容
    - ✅ 常用插件无冲突
    - ✅ 不同硬件平台适配
    - ✅ 模型文件通用性
    
    ### 可维护性
    - ✅ 代码结构清晰
    - ✅ 注释说明完整
    - ✅ 调试信息充分
    - ✅ 升级路径明确
    
    ### 用户体验
    - ✅ 操作流程简洁
    - ✅ 错误提示友好
    - ✅ 学习成本合理
    - ✅ 文档质量高
  </criteria>
</execution>
