<execution>
  <constraint>
    ## 技术环境约束
    - **ComfyUI版本兼容性**：支持最新稳定版本的节点和功能
    - **硬件资源限制**：显存使用需控制在用户硬件范围内
    - **模型文件依赖**：确保推荐的模型文件可获取且兼容
    - **生成时间约束**：单个工作流执行时间应在合理范围内
  </constraint>

  <rule>
    ## 工作流设计强制规则
    - **节点连接完整性**：每个节点的输入输出必须正确连接
    - **参数范围合规性**：所有参数设置必须在有效范围内
    - **资源管理规范**：合理控制批次大小和分辨率设置
    - **错误处理机制**：提供常见错误的诊断和解决方案
    - **版本兼容性**：确保工作流在主流ComfyUI版本中可用
  </rule>

  <guideline>
    ## 工作流设计指导原则
    - **模块化设计**：将复杂工作流拆分为可复用的模块
    - **参数可调性**：关键参数应易于调整和优化
    - **性能优化**：在质量和速度之间找到最佳平衡点
    - **用户友好**：提供清晰的节点标注和使用说明
    - **扩展性考虑**：设计时考虑后续功能扩展的可能性
  </guideline>

  <process>
    ## 视频首尾帧工作流设计流程
    
    ### Step 1: 需求分析与技术选型
    
    ```mermaid
    flowchart TD
        A[用户需求] --> B{场景类型}
        B -->|人像主导| C[IPAdapter + ControlNet方案]
        B -->|场景主导| D[ControlNet + AnimateDiff方案]
        B -->|创意场景| E[混合控制方案]
        
        C --> F[技术栈确定]
        D --> F
        E --> F
        F --> G[资源需求评估]
    ```
    
    **技术选型决策矩阵**：
    | 场景类型 | 主要技术 | 控制重点 | 推荐模型 |
    |---------|---------|---------|---------|
    | 人像写真 | IPAdapter + OpenPose | 面部一致性 | Realistic Vision |
    | 时尚展示 | ControlNet + Depth | 服装细节 | ChilloutMix |
    | 艺术创作 | AnimateDiff + Canny | 创意表达 | Anything V5 |
    
    ### Step 2: 核心工作流构建
    
    ```mermaid
    graph LR
        A[Checkpoint Loader] --> B[CLIP Text Encode]
        A --> C[VAE Loader]
        D[Load Image] --> E[IPAdapter]
        F[ControlNet Loader] --> G[Apply ControlNet]
        H[AnimateDiff Loader] --> I[AnimateDiff Combine]
        
        B --> J[KSampler]
        E --> J
        G --> J
        I --> J
        J --> K[VAE Decode]
        C --> K
        K --> L[Save Image]
    ```
    
    **关键节点配置**：
    
    #### 首帧生成配置
    ```
    KSampler设置：
    - seed: 固定种子确保可重现
    - steps: 25-30 (高质量)
    - cfg: 8-12 (强提示词遵循)
    - sampler: DPM++ 2M Karras
    - denoise: 0.4-0.6
    ```
    
    #### 尾帧生成配置
    ```
    KSampler设置：
    - seed: 首帧seed + 1000
    - steps: 20-25 (效率优化)
    - cfg: 6-9 (适度变化)
    - sampler: DDIM
    - denoise: 0.7-0.9
    ```
    
    ### Step 3: 控制器精细调节
    
    ```mermaid
    flowchart TD
        A[ControlNet配置] --> B{控制类型}
        B -->|姿态控制| C[OpenPose<br/>权重: 0.8-1.0]
        B -->|深度控制| D[Depth<br/>权重: 0.6-0.8]
        B -->|边缘控制| E[Canny<br/>权重: 0.4-0.6]
        
        C --> F[多控制器融合]
        D --> F
        E --> F
        F --> G[权重平衡优化]
    ```
    
    **IPAdapter配置策略**：
    ```
    模特一致性控制：
    - weight: 0.6-0.8 (面部特征保持)
    - weight_type: "linear"
    - start_at: 0.0
    - end_at: 0.8 (后期允许变化)
    ```
    
    ### Step 4: AnimateDiff运动控制
    
    ```mermaid
    graph TD
        A[Motion Model] --> B{运动类型}
        B -->|微动作| C[motion_scale: 0.6-0.8]
        B -->|中等动作| D[motion_scale: 0.8-1.2]
        B -->|大幅动作| E[motion_scale: 1.2-1.5]
        
        C --> F[Context设置]
        D --> F
        E --> F
        F --> G[帧数优化]
    ```
    
    ### Step 5: 批量渲染与优化
    
    ```mermaid
    flowchart LR
        A[Queue Prompt] --> B[Batch Processing]
        B --> C{显存检查}
        C -->|充足| D[继续渲染]
        C -->|不足| E[降低批次]
        D --> F[结果收集]
        E --> F
        F --> G[质量验证]
    ```
    
    **性能优化策略**：
    - 分辨率控制：512x768 → 768x1024 → 1024x1536
    - 批次管理：根据显存动态调整batch_size
    - VAE优化：使用fp16精度减少显存占用
    - 模型卸载：及时释放不需要的模型
  </process>

  <criteria>
    ## 工作流质量评价标准
    
    ### 技术指标
    - ✅ 节点连接无错误，工作流可正常执行
    - ✅ 生成速度在可接受范围内(单帧<2分钟)
    - ✅ 显存使用控制在用户硬件限制内
    - ✅ 输出质量达到商用标准
    
    ### 视觉质量
    - ✅ 模特特征在首尾帧保持一致性
    - ✅ 场景转换自然流畅
    - ✅ 光照和色彩协调统一
    - ✅ 细节清晰，无明显瑕疵
    
    ### 用户体验
    - ✅ 工作流易于理解和修改
    - ✅ 参数调节直观有效
    - ✅ 错误提示清晰明确
    - ✅ 文档说明完整详细
    
    ### 实用性评估
    - ✅ 工作流可复用于不同场景
    - ✅ 支持快速参数调整
    - ✅ 兼容主流模型和插件
    - ✅ 适合批量生产使用
  </criteria>
</execution>
