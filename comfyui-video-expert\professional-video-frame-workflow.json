{"1": {"inputs": {"ckpt_name": "Realistic\\realisticVisionV60_v60B1VAE.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "11": {"inputs": {"seed": 795158452083513, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["18", 0], "positive": ["12", 0], "negative": ["13", 0], "latent_image": ["14", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "12": {"inputs": {"text": "太阳", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "13": {"inputs": {"text": "", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "14": {"inputs": {"width": 784, "height": 1280, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "15": {"inputs": {"image": "20250804-182440.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "16": {"inputs": {"ipadapter_file": "ip-adapter-plus_sd15.safetensors"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "IPAdapter Model Loader"}}, "17": {"inputs": {"clip_name": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "加载CLIP视觉"}}, "18": {"inputs": {"weight": 1, "weight_faceidv2": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["1", 0], "ipadapter": ["16", 0], "image": ["15", 0], "clip_vision": ["17", 0]}, "class_type": "IPAdapterFaceID", "_meta": {"title": "IPAdapter FaceID"}}, "20": {"inputs": {"samples": ["11", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "21": {"inputs": {"images": ["20", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}}